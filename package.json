{"name": "native-ui", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start -c", "dev:web": "expo start -c --web", "dev:android": "expo start -c --android", "android": "expo start -c --android", "ios": "expo start -c --ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.8", "@noble/curves": "^1.9.6", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.0.0", "@rivascva/react-native-code-editor": "^1.2.2", "@rn-primitives/avatar": "~1.2.0", "@rn-primitives/dialog": "^1.2.0", "@rn-primitives/label": "^1.2.0", "@rn-primitives/popover": "^1.2.0", "@rn-primitives/portal": "~1.3.0", "@rn-primitives/progress": "~1.2.0", "@rn-primitives/radio-group": "^1.2.0", "@rn-primitives/select": "^1.2.0", "@rn-primitives/separator": "^1.2.0", "@rn-primitives/slot": "~1.2.0", "@rn-primitives/switch": "^1.2.0", "@rn-primitives/toggle-group": "^1.2.0", "@rn-primitives/tooltip": "~1.2.0", "@shopify/react-native-skia": "^2.2.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "expo": "^53.0.20", "expo-blur": "~14.1.5", "expo-camera": "~16.1.11", "expo-clipboard": "~7.1.5", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-file-system": "~18.1.11", "expo-haptics": "~14.1.4", "expo-iap": "^2.4.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.6", "expo-localization": "~16.1.6", "expo-navigation-bar": "~4.2.7", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lodash-es": "^4.17.21", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "node-forge": "git+https://github.com/Fang4321/forge.git#ecdsa", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-draggable-flatlist": "^4.0.3", "react-native-draglist": "^3.9.9", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-keyboard-controller": "^1.18.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-ssl-public-key-pinning": "^1.2.5", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "victory-native": "^41.19.2", "zustand": "^5.0.5", "expo-constants": "~17.1.7", "@react-native-async-storage/async-storage": "2.1.2"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/elliptic": "^6.4.18", "@types/lodash-es": "^4.17.12", "@types/react": "~19.0.14", "typescript": "~5.8.3"}, "private": true}