# 3X-UI 国际化测试指南

## 测试目的
验证3X-UI路由的所有页面和组件是否正确支持多语言国际化。

## 测试范围

### ✅ 已完成国际化的组件

1. **Drawer导航** (`app/3x-ui/[configId]/_layout.tsx`)
   - 概述、入站列表、路由规则、出站路由标签
   - 返回主页按钮

2. **概述页面** (`app/3x-ui/[configId]/index.tsx`)
   - Xray状态、重启、更新按钮
   - 系统资源标签（CPU、内存、磁盘）
   - 网络流量标题
   - 版本选择模态框
   - 所有错误消息和确认对话框

3. **路由规则页面** (`app/3x-ui/[configId]/routing.tsx`)
   - 添加、保存、重启、更新geo按钮
   - 空状态提示文本
   - 规则描述（域名、IP、端口等）
   - 编辑/删除操作按钮
   - 所有确认对话框和错误消息

4. **入站列表页面** (`app/3x-ui/[configId]/inbounds.tsx`)
   - 添加、用户管理按钮
   - 空状态提示文本
   - 操作菜单（编辑、导出、重置、启用/禁用、删除）
   - 所有确认对话框和状态消息

5. **出站配置页面** (`app/3x-ui/[configId]/outbounds.tsx`)
   - 添加、重启按钮
   - 空状态提示文本
   - 操作菜单（编辑、删除）
   - WARP配置模态框和所有相关功能
   - 所有确认对话框和错误消息

6. **路由规则配置模态框** (`app/3x-ui/(modal)/rule-config.tsx`)
   - 页面标题（添加/编辑）
   - 所有表单字段标签和占位符
   - 保存按钮和错误消息

7. **3X-UI组件**
   - `InboundCard`: 状态标签（有效/无效）
   - `ExportLinksModal`: 标题、按钮、状态消息

## 支持的语言

- 🇺🇸 English (en)
- 🇨🇳 简体中文 (zh-CN)
- 🇹🇼 繁體中文 (zh-TW)
- 🇮🇷 فارسی (fa)

## 测试步骤

### 1. 语言切换测试
1. 进入设置页面
2. 切换到不同语言
3. 返回3X-UI路由页面
4. 验证所有文本是否正确翻译

### 2. 页面功能测试
对每个已国际化的页面：
1. 验证页面标题正确显示
2. 验证按钮文本正确显示
3. 验证空状态文本正确显示
4. 触发错误场景，验证错误消息正确显示
5. 触发确认对话框，验证对话框文本正确显示

### 3. 模态框测试
1. 打开路由规则配置模态框
2. 验证标题根据添加/编辑模式正确显示
3. 验证所有表单字段标签正确显示
4. 验证占位符文本正确显示

### 4. 组件测试
1. 验证InboundCard状态标签正确显示
2. 打开导出链接模态框，验证所有文本正确显示

## 已知限制

### ❌ 未完成国际化的部分
- 入站配置模态框 (`inbound-config.tsx`) - 4000+行代码，内容极其复杂，已取消
- 出站配置模态框 (`outbound-config.tsx`) - 不存在或已跳过

### ✅ 新增完成的部分
- 出站配置页面 (`outbounds.tsx`) - 已完成国际化，包括WARP配置

### 🔧 需要注意的技术细节
1. 翻译函数不支持参数插值，使用 `.replace()` 方法处理动态内容
2. 某些复杂表单可能需要额外的翻译键
3. 错误消息可能需要根据实际API响应调整

## 翻译键结构

所有3X-UI相关翻译都在 `threeXUI` 命名空间下：

```typescript
threeXUI: {
  drawer: { ... },      // Drawer导航
  overview: { ... },    // 概述页面
  routing: { ... },     // 路由规则
  inbounds: { ... },    // 入站配置
  ruleConfig: { ... },  // 规则配置表单
  exportLinks: { ... }  // 导出链接
}
```

## 测试结果记录

请在测试时记录：
- [ ] 英文显示正常
- [ ] 简体中文显示正常
- [ ] 繁体中文显示正常
- [ ] 波斯语显示正常
- [ ] 语言切换功能正常
- [ ] 动态内容（如用户数量、版本号）正确替换
- [ ] 错误消息正确显示
- [ ] 确认对话框正确显示

## 后续改进建议

1. 完成出站页面的国际化
2. 完成复杂模态框的国际化
3. 添加更多语言支持
4. 优化翻译函数以支持参数插值
5. 添加自动化测试验证翻译完整性
